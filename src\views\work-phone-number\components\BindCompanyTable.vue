<template>
    <el-table
        :data="tableData"
        v-loading="loading"
        row-key="id"
        header-row-class-name="tableHeader"
        :style="{ 'min-height': height + 'px' }"
    >
        <template v-if="!loading" #empty>
            <div class="display-flex flex-column top-bottom-center">
                <img class="w-260 h-260" src="@/assets/images/customer-public-no-data.png" alt="暂无数据" />
                <div class="font-first-title-unactive color-two-grey b-margin-16">暂无数据</div>
            </div>
        </template>
        <el-table-column v-if="!props.isDialog" label="办税员号码" prop="telB"></el-table-column>
        <el-table-column v-if="!props.isDialog" label="工作号" prop="telX"></el-table-column>
        <el-table-column label="企业名称" prop="entName"></el-table-column>
        <el-table-column label="企业税号" prop="socialCreditCode"></el-table-column>
        <el-table-column label="绑定时间" prop="createTime"></el-table-column>
        <el-table-column v-if="isAdmin && !props.isDialog" label="所属租户" prop="tenantName"></el-table-column>
        <el-table-column label="操作" width="200px">
            <template #default="scope">
                <el-button type="text" @click="unbind(scope.row)">解绑</el-button>
                <el-button v-if="scope.row.bsyName && scope.row.bsySfz" type="text" @click="addTaxer(scope.row)">添加办税员</el-button>
            </template>
        </el-table-column>
    </el-table>
    <el-dialog
        title="添加办税员"
        v-model="showAddDialog"
        width="500px"
    >
        <div>
            <div class="font-14 color-black">请用法人/财务负责人身份扫码登录</div>
            <div class="ewmstyle t-margin-12 flex-center flex-column border-radius-8 border gap-8" >
                <div class="qr-container" v-loading="ewmLoading">
                    <div class="scan-frame">
                        <div class="scan-corner scan-corner-tl"></div>
                        <div class="scan-corner scan-corner-tr"></div>
                        <div class="scan-corner scan-corner-bl"></div>
                        <div class="scan-corner scan-corner-br"></div>
                        <vue-qr
                            class="qr-code"
                            ref="qrcode"
                            :title="collectUrl"
                            :text="collectUrl"
                            :size="120"
                            :margin="0"
                        ></vue-qr>
                    </div>
                </div>
                <span>失效时间：{{ endTime || '' }}</span>
            </div>
        </div>
    </el-dialog>
</template>

<script lang="ts" setup>
import { ref, defineProps, watch, computed } from 'vue'
import type { IBindCompanyTableDataItem, IWorkCompanyAuthParams } from '@/types/worknumber'
import { ElMessage, ElMessageBox } from 'element-plus'
import aicService from '@/service/aicService'
import { useStore } from 'vuex'
import vueQr from 'vue-qr/src/packages/vue-qr.vue'

const store = useStore()
const isAdmin = computed(() => {
    let user = store.state.user?.account?.user || []
    if (user.role.includes('admin') || user.role.includes('yunwei')) {
        return true
    } else {
        return false
    }
})
const props = defineProps<{
    tableDataList: IBindCompanyTableDataItem[]
    tableLoading: boolean
    tableHeight: number
    refresh:() => void
    isDialog:boolean
}>()
const tableData = ref<IBindCompanyTableDataItem[]>()
watch(() => props.tableDataList, (newVal) => {
    // console.log('newVal',newVal)
    tableData.value = newVal
}, { immediate: true })
const loading = ref(false)
watch(() => props.tableLoading, (newVal) => {
    loading.value = newVal
}, { immediate: true })
const height = ref(400)
watch(() => props.tableHeight, (newVal) => {
    height.value = newVal
},{ immediate: true })
const unbind = (row:IBindCompanyTableDataItem) => {
    console.log(row)
    ElMessageBox.confirm('是否确认解绑?',
        '确认',
        {
            confirmButtonText: '解绑',
            cancelButtonText: '取消',
            type: 'warning',
        }).then(() => {
        aicService.unbindEnterprise({id:row.id}).then((response) => {
            console.log('unbind', response)
            if(response.success){
                ElMessage.success('解绑成功')
                props.refresh()
            }else{
                ElMessage.error(response.errMsg)
            }
        })
    })
}
const ewmLoading = ref(false)
const showAddDialog = ref(false)
const collectUrl = ref('')
const endTime = ref('')
const addTaxer = (row:IBindCompanyTableDataItem) => {
    ewmLoading.value = true
    endTime.value = new Date(new Date().getTime() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16)
    console.log(row)
    showAddDialog.value = true
    const params : IWorkCompanyAuthParams = {
        nsrsbh:row.socialCreditCode,
        qymc:row.entName,
        bsysfzhm:row.bsySfz,
        bsyxm:row.bsyName,
        bsysjhm:row.telB,
        sflx:'BSY',
        tenantId:row.tenantId,
    }
    aicService.companyAuth(params).then((res) => {
        console.log('res',res)
        collectUrl.value = res.authUrl
        ewmLoading.value = false
    })    
}

</script>

<style lang="scss" scoped>
@use '@/styles/element-lead.scss';
.ewmstyle{
    height: 250px;
    border: 1px solid #DEDEDF;
    background: linear-gradient(to bottom, #EDF3FF, #FFFFFF);
}

.qr-container {
    padding: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.scan-frame {
    position: relative;
    display: inline-block;
    padding: 20px;
}

.scan-corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 3px solid #1890ff;
}

.scan-corner-tl {
    top: 0;
    left: 0;
    border-right: none;
    border-bottom: none;
}

.scan-corner-tr {
    top: 0;
    right: 0;
    border-left: none;
    border-bottom: none;
}

.scan-corner-bl {
    bottom: 0;
    left: 0;
    border-right: none;
    border-top: none;
}

.scan-corner-br {
    bottom: 0;
    right: 0;
    border-left: none;
    border-top: none;
}

.qr-code {
    display: block;
}

</style>
